#!/usr/bin/env python3
"""
Complete Psychometrist Portal Workflow
Combines document analysis results and generates comprehensive AI reports
"""

import os
import json
from datetime import datetime
from pathlib import Path
from src.utils.document_analysis_results import analyze_patient_documents
from src.ai.ai_document_analyzer import analyze_document
from src.ai.ai_report_generator import generate_psychological_report, save_report_to_file

def process_patient_complete_workflow(patient_folder_path):
    """
    Complete workflow: Analyze documents → Generate comprehensive report
    """
    print("🚀 COMPLETE PSYCHOMETRIST PORTAL WORKFLOW")
    print("=" * 80)

    # Step 1: Analyze all documents in patient folder
    print("📋 STEP 1: DOCUMENT ANALYSIS RESULTS")
    print("-" * 50)

    analysis_results = analyze_patient_documents(patient_folder_path)

    if "error" in analysis_results:
        return {"error": f"Document analysis failed: {analysis_results['error']}"}

    print(f"✅ Successfully analyzed {analysis_results['summary']['successful_analyses']}/{analysis_results['summary']['total_documents']} documents")
    print(f"📊 Average confidence: {analysis_results['summary']['average_confidence']}%")

    # Step 2: Get detailed analysis for high-value documents
    print("\n📖 STEP 2: DETAILED DOCUMENT ANALYSIS")
    print("-" * 50)

    detailed_analyses = get_detailed_analyses(analysis_results)

    if "error" in detailed_analyses:
        return {"error": f"Detailed analysis failed: {detailed_analyses['error']}"}

    print(f"✅ Generated detailed analyses for {len(detailed_analyses['analyses'])} key documents")

    # Step 3: Generate comprehensive psychological report
    print("\n📝 STEP 3: GENERATE COMPREHENSIVE REPORT")
    print("-" * 50)

    report_result = generate_psychological_report(detailed_analyses['analyses'])

    if report_result.get("status") != "SUCCESS":
        return {"error": f"Report generation failed: {report_result.get('error')}"}

    print("✅ Comprehensive psychological report generated successfully")

    # Step 4: Save and organize results
    print("\n💾 STEP 4: SAVE RESULTS")
    print("-" * 50)

    patient_name = detailed_analyses.get('patient_name', 'Unknown_Patient')

    # Save comprehensive report
    report_filepath = save_report_to_file(report_result["report"], patient_name, "reports")

    # Save analysis results
    analysis_filepath = save_analysis_results(analysis_results, patient_name)

    # Create summary
    workflow_summary = {
        "patient_name": patient_name,
        "patient_folder": analysis_results["patient_folder"],
        "timestamp": datetime.now().isoformat(),
        "documents_analyzed": analysis_results["summary"]["successful_analyses"],
        "total_documents": analysis_results["summary"]["total_documents"],
        "average_confidence": analysis_results["summary"]["average_confidence"],
        "categories_found": analysis_results["summary"]["categories_found"],
        "report_filepath": report_filepath,
        "analysis_filepath": analysis_filepath,
        "status": "SUCCESS"
    }

    print(f"📄 Report saved to: {report_filepath}")
    print(f"📊 Analysis saved to: {analysis_filepath}")

    return workflow_summary

def get_detailed_analyses(analysis_results):
    """
    Get detailed AI analysis for high-value documents
    """
    detailed_analyses = []
    patient_name = "Unknown"

    # Priority order for document types (most important first)
    priority_categories = [
        "Cognitive Tests",
        "Academic Tests",
        "Inventories",
        "Background Info",
        "Behavioral Observation Scales"
    ]

    # Get high-value documents from each category
    for category in priority_categories:
        category_docs = analysis_results["categories"].get(category, [])

        # Sort by confidence and clinical value
        high_value_docs = []
        for doc in category_docs:
            if doc.get("status") == "SUCCESS" and doc.get("classification", {}).get("clinical_value") in ["High", "Medium"]:
                try:
                    confidence = int(doc.get("classification", {}).get("confidence", 0))
                    if confidence >= 85:
                        high_value_docs.append(doc)
                except (ValueError, TypeError):
                    # Skip documents with invalid confidence scores
                    continue

        # Take top 2-3 documents per category to avoid overwhelming the AI
        for doc in high_value_docs[:3]:
            print(f"   🔍 Analyzing: {doc['filename']}")

            # Get detailed psychological analysis for this document
            print(f"      📄 Reading: {doc['filepath']}")

            # Use the existing analyze_document function which gives us full psychological analysis
            detailed_result = analyze_document(doc["filepath"])

            if isinstance(detailed_result, dict) and "error" not in detailed_result:
                detailed_analyses.append(detailed_result)

                # Extract patient name from first successful analysis
                if patient_name == "Unknown" and "patient_info" in detailed_result:
                    patient_name = detailed_result["patient_info"].get("name", "Unknown")

                print(f"      ✅ Success: {detailed_result.get('document_type', 'Unknown type')}")
            else:
                print(f"      ❌ Failed to analyze: {doc['filename']}")

    return {
        "analyses": detailed_analyses,
        "patient_name": patient_name,
        "total_detailed": len(detailed_analyses)
    }

def save_analysis_results(analysis_results, patient_name):
    """
    Save analysis results to file
    """
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    safe_name = "".join(c for c in patient_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
    filename = f"{safe_name}_{timestamp}_analysis_results.json"

    os.makedirs("reports", exist_ok=True)
    filepath = os.path.join("reports", filename)

    with open(filepath, 'w', encoding='utf-8') as f:
        json.dump(analysis_results, f, indent=2)

    return filepath

def display_workflow_summary(summary):
    """
    Display a summary of the complete workflow results
    """
    print("\n" + "=" * 80)
    print("📊 PSYCHOMETRIST PORTAL - WORKFLOW COMPLETE")
    print("=" * 80)

    print(f"👤 Patient: {summary['patient_name']}")
    print(f"📁 Folder: {summary['patient_folder']}")
    print(f"⏰ Completed: {summary['timestamp']}")
    print()

    print("📋 DOCUMENT ANALYSIS SUMMARY:")
    print(f"   📄 Documents Processed: {summary['documents_analyzed']}/{summary['total_documents']}")
    print(f"   🎯 Average Confidence: {summary['average_confidence']}%")
    print()

    print("📊 CATEGORIES FOUND:")
    for category, count in summary['categories_found'].items():
        print(f"   {category}: {count} documents")
    print()

    print("📝 OUTPUT FILES:")
    print(f"   📄 Psychological Report: {summary['report_filepath']}")
    print(f"   📊 Analysis Results: {summary['analysis_filepath']}")
    print()

    print("✅ WORKFLOW STATUS: SUCCESS")
    print("🚀 Ready for psychometrist review!")
    print("=" * 80)

def main():
    """
    Test the complete workflow with Viktor's patient data
    """
    # Test with Viktor's patient folder
    test_folder = "OWL Downloads/1168_NRa_2019-12-07_2025-05-02"

    print("🧪 TESTING COMPLETE PSYCHOMETRIST PORTAL WORKFLOW")
    print("=" * 80)
    print(f"📁 Processing patient folder: {test_folder}")
    print()

    # Run complete workflow
    result = process_patient_complete_workflow(test_folder)

    if "error" in result:
        print(f"❌ WORKFLOW FAILED: {result['error']}")
    else:
        # Display summary
        display_workflow_summary(result)

        # Show first part of generated report
        print("\n📖 GENERATED REPORT PREVIEW:")
        print("-" * 50)
        try:
            with open(result['report_filepath'], 'r', encoding='utf-8') as f:
                report_content = f.read()
                # Show first 1000 characters
                print(report_content[:1000] + "..." if len(report_content) > 1000 else report_content)
        except Exception as e:
            print(f"Could not preview report: {e}")

    print("\n🎉 COMPLETE WORKFLOW TEST FINISHED!")

if __name__ == "__main__":
    main()
