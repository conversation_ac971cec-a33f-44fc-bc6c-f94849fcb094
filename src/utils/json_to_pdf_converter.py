#!/usr/bin/env python3
"""
JSON to PDF Report Converter
Converts report_preview.json into a professional PDF psychological report
"""
import json
import os
from datetime import datetime
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, PageBreak
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
from reportlab.lib.enums import TA_LEFT, TA_CENTER, TA_JUSTIFY
import re
from bs4 import BeautifulSoup

def load_report_preview(json_file="report_preview.json"):
    """
    Load the report preview from JSON file
    """
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"Error: {json_file} not found")
        return None
    except json.JSONDecodeError as e:
        print(f"Error parsing JSON: {e}")
        return None

def clean_html_text(html_content):
    """
    Convert HTML content to plain text, preserving formatting
    """
    if not html_content:
        return ""
    
    # Parse HTML
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # Remove script and style elements
    for script in soup(["script", "style"]):
        script.decompose()
    
    # Handle different HTML elements
    text_parts = []
    
    for element in soup.find_all(['p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'ul', 'ol', 'li', 'br', 'table']):
        if element.name == 'p':
            text_parts.append(element.get_text().strip())
        elif element.name.startswith('h'):
            # Headers
            level = int(element.name[1])
            text_parts.append(f"\n{'=' * (7 - level)} {element.get_text().strip()} {'=' * (7 - level)}\n")
        elif element.name in ['ul', 'ol']:
            # Lists
            for li in element.find_all('li'):
                text_parts.append(f"• {li.get_text().strip()}")
        elif element.name == 'li':
            text_parts.append(f"• {element.get_text().strip()}")
        elif element.name == 'br':
            text_parts.append("\n")
        elif element.name == 'table':
            # Handle tables
            table_text = []
            for row in element.find_all('tr'):
                row_text = []
                for cell in row.find_all(['td', 'th']):
                    row_text.append(cell.get_text().strip())
                if row_text:
                    table_text.append(" | ".join(row_text))
            if table_text:
                text_parts.append("\n".join(table_text))
    
    # If no structured elements found, get all text
    if not text_parts:
        text_parts.append(soup.get_text().strip())
    
    return "\n".join(text_parts)

def create_percentile_table():
    """
    Create the percentile ranges table for assessment results
    """
    data = [
        ['Verbal Descriptor', 'Percentile Range'],
        ['Exceptionally Low', '1st - 2nd'],
        ['Below Average', '3rd - 24th'],
        ['Low Average', '25th - 39th'],
        ['Average', '40th - 59th'],
        ['High Average', '60th - 74th'],
        ['Above Average', '75th - 89th'],
        ['Exceptionally High', '90th - 99th']
    ]
    
    table = Table(data, colWidths=[2.5*inch, 2*inch])
    table.setStyle(TableStyle([
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),  # Header row
        ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),      # Data rows
        ('FONTSIZE', (0, 0), (-1, -1), 10),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
    ]))
    
    return table

def create_custom_styles():
    """
    Create custom styles for the PDF
    """
    styles = getSampleStyleSheet()
    
    # Custom title style
    styles.add(ParagraphStyle(
        name='CustomTitle',
        parent=styles['Title'],
        fontSize=16,
        spaceAfter=30,
        alignment=TA_CENTER,
        textColor=colors.darkblue
    ))
    
    # Custom heading style
    styles.add(ParagraphStyle(
        name='CustomHeading',
        parent=styles['Heading1'],
        fontSize=14,
        spaceAfter=12,
        spaceBefore=20,
        textColor=colors.darkblue,
        alignment=TA_LEFT
    ))
    
    # Custom subheading style
    styles.add(ParagraphStyle(
        name='CustomSubHeading',
        parent=styles['Heading2'],
        fontSize=12,
        spaceAfter=8,
        spaceBefore=16,
        textColor=colors.darkblue,
        alignment=TA_LEFT
    ))
    
    # Custom body style
    styles.add(ParagraphStyle(
        name='CustomBody',
        parent=styles['Normal'],
        fontSize=10,
        spaceAfter=6,
        alignment=TA_JUSTIFY,
        leading=14
    ))
    
    return styles

def extract_patient_info(report_data):
    """
    Extract patient information from the front page
    """
    patient_info = {}
    
    if 'front_page' in report_data:
        front_page_content = list(report_data['front_page'].values())[0]
        # Parse the table to extract patient info
        soup = BeautifulSoup(front_page_content, 'html.parser')
        table = soup.find('table')
        
        if table:
            for row in table.find_all('tr'):
                cells = row.find_all(['td', 'th'])
                if len(cells) >= 2:
                    key = cells[0].get_text().strip()
                    value = cells[1].get_text().strip()
                    patient_info[key] = value
    
    return patient_info

def create_pdf_report(report_data, output_filename=None):
    """
    Create PDF report from the JSON data
    """
    if not report_data:
        print("No report data to convert")
        return
    
    # Create output filename if not provided
    if not output_filename:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_filename = f"Psychological_Report_{timestamp}.pdf"
    
    # Create PDF document
    doc = SimpleDocTemplate(output_filename, pagesize=A4)
    styles = create_custom_styles()
    story = []
    
    # Extract patient info
    patient_info = extract_patient_info(report_data)
    
    # Add title page
    story.append(Paragraph("PSYCHOLOGICAL REPORT", styles['CustomTitle']))
    story.append(Paragraph("(Private and Confidential)", styles['CustomTitle']))
    story.append(Spacer(1, 30))
    
    # Add patient information table if available
    if patient_info:
        patient_data = []
        for key, value in patient_info.items():
            if value:  # Only add non-empty values
                patient_data.append([key, value])
        
        if patient_data:
            patient_table = Table(patient_data, colWidths=[2*inch, 4*inch])
            patient_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
                ('FONTNAME', (1, 0), (1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
            ]))
            story.append(patient_table)
            story.append(Spacer(1, 20))
    
    story.append(PageBreak())
    
    # Define section order
    section_order = [
        "reason_referral",
        "background", 
        "assessment_methods",
        "behavioral_observations",
        "assessment_results",
        "clinical_impressions",
        "recommendations"
    ]
    
    section_titles = {
        "reason_referral": "REASON FOR REFERRAL",
        "background": "BACKGROUND INFORMATION",
        "assessment_methods": "ASSESSMENT METHODS",
        "behavioral_observations": "BEHAVIORAL OBSERVATIONS",
        "assessment_results": "ASSESSMENT RESULTS",
        "clinical_impressions": "CLINICAL IMPRESSIONS AND DIAGNOSES",
        "recommendations": "RECOMMENDATIONS"
    }
    
    # Process each section
    for section_key in section_order:
        if section_key in report_data:
            # Add section heading
            story.append(Paragraph(section_titles.get(section_key, section_key.upper()), styles['CustomHeading']))
            story.append(Spacer(1, 12))
            
            # Process subsections
            section_data = report_data[section_key]
            if isinstance(section_data, dict):
                for subsection_name, subsection_content in section_data.items():
                    # Add subsection heading
                    story.append(Paragraph(subsection_name, styles['CustomSubHeading']))
                    
                    # Special handling for percentile ranges subsection
                    if subsection_name == "Percentile Ranges and Descriptors":
                        # Add the standard percentile explanation
                        percentile_text = """Results are often reported as percentiles. A percentile refers to the percent of same-age peers falling below the score obtained by a given student. For example, if a student's score is at the 30th percentile, this means that she scored better than 30% of students her age who took the same test. Scores falling between the 25th and the 74th percentiles represent an age-appropriate level of skill development. The table below provides the percentile ranges for verbal descriptors used throughout this report (e.g., average range)."""
                        
                        story.append(Paragraph(percentile_text, styles['CustomBody']))
                        story.append(Spacer(1, 12))
                        
                        # Add the percentile table
                        percentile_table = create_percentile_table()
                        story.append(percentile_table)
                        story.append(Spacer(1, 12))
                    else:
                        # Convert HTML content to text for other subsections
                        clean_text = clean_html_text(subsection_content)
                        
                        # Split into paragraphs and add to story
                        paragraphs = clean_text.split('\n\n')
                        for para in paragraphs:
                            para = para.strip()
                            if para:
                                # Handle bullet points
                                if para.startswith('•'):
                                    # Convert to bullet list
                                    items = para.split('\n')
                                    for item in items:
                                        item = item.strip()
                                        if item:
                                            story.append(Paragraph(item, styles['CustomBody']))
                                else:
                                    story.append(Paragraph(para, styles['CustomBody']))
                    
                    story.append(Spacer(1, 8))
            
            story.append(Spacer(1, 20))
    
    # Build PDF
    try:
        doc.build(story)
        print(f"PDF report successfully created: {output_filename}")
        return output_filename
    except Exception as e:
        print(f"Error creating PDF: {e}")
        return None

def main():
    """
    Main function to convert JSON to PDF
    """
    print("JSON to PDF Report Converter")
    print("=" * 40)
    
    # Load report data
    report_data = load_report_preview()
    if not report_data:
        return
    
    print("Report data loaded successfully")
    
    # Create PDF
    output_file = create_pdf_report(report_data)
    
    if output_file:
        print(f"\nConversion complete!")
        print(f"PDF saved as: {output_file}")
    else:
        print("\nConversion failed!")

if __name__ == "__main__":
    main() 