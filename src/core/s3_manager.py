#!/usr/bin/env python3
"""
AWS S3 Manager for Psychometrist Portal
Handles all S3 operations for document storage and retrieval
"""

import os
import boto3
import json
from datetime import datetime, timedelta
from pathlib import Path
from typing import Optional, Dict, Any, List
from dotenv import load_dotenv
import uuid
import tempfile

# Load environment variables
load_dotenv()

class S3Manager:
    def __init__(self):
        """Initialize S3 client with credentials from environment"""
        self.aws_access_key = os.getenv('AWS_ACCESS_KEY')
        self.aws_secret_key = os.getenv('AWS_SECRET')
        self.bucket_name = os.getenv('AWS_S3_BUCKET')
        self.region = os.getenv('AWS_S3_REGION', 'us-east-1')

        if not all([self.aws_access_key, self.aws_secret_key, self.bucket_name]):
            raise ValueError("Missing AWS credentials in environment variables")

        # Initialize S3 client
        self.s3_client = boto3.client(
            's3',
            aws_access_key_id=self.aws_access_key,
            aws_secret_access_key=self.aws_secret_key,
            region_name=self.region
        )

        # S3 folder structure
        self.folders = {
            'uploads': 'uploads/{patient_id}/patient-documents/',
            'reports': 'reports/generated-reports/',
            'cheatsheets': 'cheatsheets/',
            'analysis_results': 'analysis-results/',
            'temp': 'temp/'
        }

    def upload_file(self, file_content: bytes, filename: str, folder: str = 'uploads',
                   patient_id: Optional[str] = None, metadata: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
        """
        Upload file to S3

        Args:
            file_content: File content as bytes
            filename: Original filename
            folder: S3 folder ('uploads', 'reports', 'cheatsheets', etc.)
            patient_id: Patient ID for organizing uploads (required for 'uploads' folder)
            metadata: Additional metadata to store with file

        Returns:
            Dict with file info and S3 details
        """
        try:
            # Validate patient_id for uploads folder
            if folder == 'uploads' and not patient_id:
                raise ValueError("patient_id is required for uploads folder")

            # Generate unique filename
            file_id = str(uuid.uuid4())
            file_extension = Path(filename).suffix
            unique_filename = f"{file_id}{file_extension}"

            # Create S3 key with patient_id if needed
            folder_path = self.folders[folder]
            if folder == 'uploads':
                folder_path = folder_path.format(patient_id=patient_id)
            s3_key = f"{folder_path}{unique_filename}"

            # Prepare metadata
            s3_metadata = {
                'original-filename': filename,
                'file-id': file_id,
                'upload-timestamp': datetime.now().isoformat(),
                'folder': folder
            }
            if patient_id:
                s3_metadata['patient-id'] = patient_id

            if metadata:
                s3_metadata.update(metadata)

            # Upload to S3
            self.s3_client.put_object(
                Bucket=self.bucket_name,
                Key=s3_key,
                Body=file_content,
                Metadata=s3_metadata,
                ContentType=self._get_content_type(file_extension)
            )

            return {
                'file_id': file_id,
                'filename': filename,
                'unique_filename': unique_filename,
                's3_key': s3_key,
                's3_bucket': self.bucket_name,
                's3_url': f"s3://{self.bucket_name}/{s3_key}",
                'size': len(file_content),
                'upload_time': datetime.now().isoformat(),
                'status': 'uploaded',
                'metadata': s3_metadata
            }

        except Exception as e:
            raise Exception(f"S3 upload failed: {str(e)}")

    def download_file(self, s3_key: str) -> bytes:
        """
        Download file from S3

        Args:
            s3_key: S3 object key

        Returns:
            File content as bytes
        """
        try:
            response = self.s3_client.get_object(Bucket=self.bucket_name, Key=s3_key)
            return response['Body'].read()

        except Exception as e:
            raise Exception(f"S3 download failed: {str(e)}")

    def download_file_to_temp(self, s3_key: str) -> str:
        """
        Download file from S3 to temporary local file

        Args:
            s3_key: S3 object key

        Returns:
            Path to temporary file
        """
        try:
            # Get file extension from S3 key
            file_extension = Path(s3_key).suffix

            # Create temporary file with correct extension
            with tempfile.NamedTemporaryFile(delete=False, suffix=file_extension) as temp_file:
                temp_path = temp_file.name

                # Download from S3
                self.s3_client.download_file(self.bucket_name, s3_key, temp_path)

                return temp_path

        except Exception as e:
            raise Exception(f"S3 download to temp failed: {str(e)}")

    def get_file_metadata(self, s3_key: str) -> Dict[str, Any]:
        """
        Get file metadata from S3

        Args:
            s3_key: S3 object key

        Returns:
            File metadata
        """
        try:
            response = self.s3_client.head_object(Bucket=self.bucket_name, Key=s3_key)

            return {
                'size': response['ContentLength'],
                'last_modified': response['LastModified'].isoformat(),
                'content_type': response.get('ContentType'),
                'metadata': response.get('Metadata', {}),
                's3_key': s3_key,
                's3_bucket': self.bucket_name
            }

        except Exception as e:
            raise Exception(f"Failed to get S3 metadata: {str(e)}")

    def list_files(self, folder: str = 'uploads', prefix: str = '', patient_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        List files in S3 folder

        Args:
            folder: S3 folder to list
            prefix: Additional prefix filter
            patient_id: Filter files by patient ID (for 'uploads' folder)

        Returns:
            List of file information
        """
        try:
            # Handle patient-specific listing for uploads folder
            if folder == 'uploads':
                if patient_id:
                    s3_prefix = f"{self.folders[folder].format(patient_id=patient_id)}{prefix}"
                else:
                    # List all patient folders
                    s3_prefix = "uploads/"
            else:
                s3_prefix = f"{self.folders[folder]}{prefix}"

            response = self.s3_client.list_objects_v2(
                Bucket=self.bucket_name,
                Prefix=s3_prefix
            )

            files = []
            for obj in response.get('Contents', []):
                # Get metadata for each file
                try:
                    metadata_response = self.s3_client.head_object(
                        Bucket=self.bucket_name,
                        Key=obj['Key']
                    )
                    metadata = metadata_response.get('Metadata', {})
                except:
                    metadata = {}

                # Extract patient ID from path for uploads folder
                if folder == 'uploads':
                    path_parts = obj['Key'].split('/')
                    if len(path_parts) > 2:  # uploads/<patient_id>/patient-documents/...
                        metadata['patient-id'] = path_parts[1]

                files.append({
                    's3_key': obj['Key'],
                    'filename': metadata.get('original-filename', Path(obj['Key']).name),
                    'size': obj['Size'],
                    'last_modified': obj['LastModified'].isoformat(),
                    'metadata': metadata
                })

            # For reports, filter by patient_id if provided
            if folder == 'reports' and patient_id:
                files = [f for f in files if f['metadata'].get('patient-id') == patient_id]

            return sorted(files, key=lambda x: x['last_modified'], reverse=True)

        except Exception as e:
            raise Exception(f"Failed to list S3 files: {str(e)}")

    def delete_file(self, s3_key: str) -> bool:
        """
        Delete file from S3

        Args:
            s3_key: S3 object key

        Returns:
            True if successful
        """
        try:
            self.s3_client.delete_object(Bucket=self.bucket_name, Key=s3_key)
            return True

        except Exception as e:
            raise Exception(f"S3 delete failed: {str(e)}")

    def generate_presigned_url(self, s3_key: str, expiration: int = 3600,
                              operation: str = 'get_object') -> str:
        """
        Generate presigned URL for secure file access

        Args:
            s3_key: S3 object key
            expiration: URL expiration time in seconds (default 1 hour)
            operation: S3 operation ('get_object' or 'put_object')

        Returns:
            Presigned URL
        """
        try:
            url = self.s3_client.generate_presigned_url(
                operation,
                Params={'Bucket': self.bucket_name, 'Key': s3_key},
                ExpiresIn=expiration
            )
            return url

        except Exception as e:
            raise Exception(f"Failed to generate presigned URL: {str(e)}")

    def upload_json_data(self, data: Dict[str, Any], filename: str,
                        folder: str = 'analysis_results', metadata: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
        """
        Upload JSON data to S3

        Args:
            data: Dictionary to upload as JSON
            filename: Filename for the JSON file
            folder: S3 folder
            metadata: Additional metadata to store with file

        Returns:
            Upload result
        """
        try:
            json_content = json.dumps(data, indent=2, default=str).encode('utf-8')

            return self.upload_file(
                file_content=json_content,
                filename=filename,
                folder=folder,
                metadata=metadata or {'content-type': 'application/json'}
            )

        except Exception as e:
            raise Exception(f"Failed to upload JSON to S3: {str(e)}")

    def download_json_data(self, s3_key: str) -> Dict[str, Any]:
        """
        Download and parse JSON data from S3

        Args:
            s3_key: S3 object key

        Returns:
            Parsed JSON data
        """
        try:
            content = self.download_file(s3_key)
            return json.loads(content.decode('utf-8'))

        except Exception as e:
            raise Exception(f"Failed to download JSON from S3: {str(e)}")

    def _get_content_type(self, file_extension: str) -> str:
        """Get content type based on file extension"""
        content_types = {
            '.pdf': 'application/pdf',
            '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            '.doc': 'application/msword',
            '.txt': 'text/plain',
            '.json': 'application/json',
            '.csv': 'text/csv',
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.png': 'image/png'
        }
        return content_types.get(file_extension.lower(), 'application/octet-stream')

    def health_check(self) -> Dict[str, Any]:
        """
        Check S3 connection and bucket access

        Returns:
            Health status
        """
        try:
            # Try to list bucket contents (limited)
            self.s3_client.list_objects_v2(Bucket=self.bucket_name, MaxKeys=1)

            return {
                'status': 'healthy',
                'bucket': self.bucket_name,
                'region': self.region,
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            return {
                'status': 'unhealthy',
                'error': str(e),
                'bucket': self.bucket_name,
                'region': self.region,
                'timestamp': datetime.now().isoformat()
            }

# Global S3 manager instance
s3_manager = S3Manager()
