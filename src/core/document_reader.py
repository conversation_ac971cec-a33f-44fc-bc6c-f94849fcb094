#!/usr/bin/env python3
"""
Simple Document Reader for Psychometrist Portal
Reads PDF, DOCX, and CSV files and extracts text content
"""

import os
import sys
from pathlib import Path

def detect_file_type(file_path):
    """
    Detect what type of file we're dealing with
    """
    file_path = Path(file_path)
    extension = file_path.suffix.lower()

    if extension == '.pdf':
        return 'PDF'
    elif extension in ['.docx', '.doc']:
        return 'DOCX'
    elif extension == '.csv':
        return 'CSV'
    elif extension == '.txt':
        return 'TXT'
    else:
        return 'UNKNOWN'

def read_pdf(file_path):
    """
    Read text from PDF file
    """
    try:
        import PyPDF2

        with open(file_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            text = ""

            for page_num in range(len(pdf_reader.pages)):
                page = pdf_reader.pages[page_num]
                text += page.extract_text() + "\n"

            return text.strip()

    except ImportError:
        return "ERROR: PyPDF2 library not installed. Run: pip install PyPDF2"
    except Exception as e:
        return f"ERROR reading PDF: {str(e)}"

def read_docx(file_path):
    """
    Read text from DOCX file
    """
    try:
        from docx import Document

        doc = Document(file_path)
        text = ""

        for paragraph in doc.paragraphs:
            text += paragraph.text + "\n"

        return text.strip()

    except ImportError:
        return "ERROR: python-docx library not installed. Run: pip install python-docx"
    except Exception as e:
        return f"ERROR reading DOCX: {str(e)}"

def read_csv(file_path):
    """
    Read data from CSV file
    """
    try:
        import pandas as pd

        df = pd.read_csv(file_path)
        return df.to_string()

    except ImportError:
        return "ERROR: pandas library not installed. Run: pip install pandas"
    except Exception as e:
        return f"ERROR reading CSV: {str(e)}"

def read_txt(file_path):
    """
    Read text from TXT file
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            return file.read()

    except Exception as e:
        return f"ERROR reading TXT: {str(e)}"

def read_document(file_path):
    """
    Main function to read any document type
    """
    if not os.path.exists(file_path):
        return f"ERROR: File not found: {file_path}"

    file_type = detect_file_type(file_path)
    print(f"📄 File Type: {file_type}")
    print(f"📁 File Path: {file_path}")
    print("=" * 50)

    if file_type == 'PDF':
        content = read_pdf(file_path)
    elif file_type == 'DOCX':
        content = read_docx(file_path)
    elif file_type == 'CSV':
        content = read_csv(file_path)
    elif file_type == 'TXT':
        content = read_txt(file_path)
    else:
        content = f"ERROR: Unsupported file type: {file_type}"

    return content

def main():
    """
    Test the document reader with multiple file types
    """
    # Test files of different types
    test_files = [
        "OWL Downloads/1148_VGo_2019-11-02_2025-05-02/documents/0_ViGo_Referral.pdf",  # PDF
        "OWL Downloads/1148_VGo_2019-11-02_2025-05-02/logs/VGo_logs.csv",  # CSV
        "OWL Downloads/1148_VGo_2019-11-02_2025-05-02/documents/58_Viktor_Comments_in_Word.docx"  # DOCX
    ]

    print("🔍 DOCUMENT READER TEST - MULTIPLE FILE TYPES")
    print("=" * 60)

    for i, test_file in enumerate(test_files, 1):
        print(f"\n📋 TEST {i}/3")
        print("-" * 40)

        content = read_document(test_file)

        # Show first 500 characters to keep output manageable
        if len(content) > 500:
            print(content[:500] + "\n... (truncated)")
        else:
            print(content)

        print("-" * 40)

    print("\n" + "=" * 60)
    print("✅ All tests completed!")

if __name__ == "__main__":
    main()
