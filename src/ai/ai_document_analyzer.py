#!/usr/bin/env python3
"""
AI Document Analyzer for Psychometrist Portal
Uses Groq AI to analyze psychological assessment documents
"""

import os
import json
from dotenv import load_dotenv
from src.core.document_reader import read_document
from src.core.openai_client import setup_openai_client

# Load environment variables
load_dotenv()

def create_analysis_prompt(document_text):
    """
    Create a prompt for AI to analyze the psychological document
    """
    prompt = f"""
You are an expert psychologist analyzing a psychological assessment document.

Please analyze the following document and provide ONLY a valid JSON response with no additional text, explanations, or markdown formatting.

DOCUMENT TEXT:
{document_text}

Respond with ONLY this JSON structure (no markdown, no explanations):
{{
    "document_type": "Type of document (e.g., 'Referral Form', 'WISC Intelligence Test', 'BASC Behavioral Assessment', etc.)",
    "patient_info": {{
        "name": "Patient name if found",
        "age": "Patient age if found",
        "date_of_birth": "DOB if found",
        "gender": "Gender if found"
    }},
    "assessment_details": {{
        "test_name": "Full name of psychological test if applicable",
        "test_date": "Date of assessment if found",
        "examiner": "Name of examiner/psychologist if found"
    }},
    "key_scores": [
        "List any important scores, percentiles, or measurements found"
    ],
    "main_findings": [
        "List the main findings, observations, or conclusions"
    ],
    "recommendations": [
        "List any recommendations or next steps mentioned"
    ],
    "confidence_level": "High/Medium/Low - how confident are you in this analysis?"
}}

Rules:
- Only include information clearly stated in the document
- If information is not found, use "Not found" or empty array []
- Return ONLY valid JSON, no other text
"""
    return prompt

def analyze_document_with_ai(document_text, client, model):
    """
    Send document to OpenAI for analysis
    """
    try:
        prompt = create_analysis_prompt(document_text)

        response = client.chat.completions.create(
            model=model,
            messages=[
                {"role": "user", "content": prompt}
            ],
            temperature=0.1,  # Low temperature for consistent, factual responses
            max_tokens=4096
        )

        ai_response = response.choices[0].message.content

        # Try to parse as JSON
        try:
            # First try direct parsing
            analysis = json.loads(ai_response)
            return analysis, "SUCCESS"
        except json.JSONDecodeError:
            # Try to extract JSON from markdown code blocks
            import re
            json_match = re.search(r'```(?:json)?\\s*(\{.*?\})\\s*```', ai_response, re.DOTALL)
            if json_match:
                try:
                    analysis = json.loads(json_match.group(1))
                    return analysis, "SUCCESS"
                except json.JSONDecodeError:
                    pass

            # Try to find JSON-like content
            json_match = re.search(r'(\{.*\})', ai_response, re.DOTALL)
            if json_match:
                try:
                    analysis = json.loads(json_match.group(1))
                    return analysis, "SUCCESS"
                except json.JSONDecodeError:
                    pass

            # If all else fails, return raw response
            return {"raw_response": ai_response, "error": "Could not parse as JSON"}, "PARTIAL_SUCCESS"

    except Exception as e:
        print("OpenAI API error in analyze_document_with_ai:", e)
        if hasattr(e, 'response'):
            print("OpenAI response:", getattr(e.response, 'text', e.response))
        return {"error": str(e)}, "ERROR"

def analyze_document(file_path):
    """
    Main function to analyze a document using AI
    """
    print(f"🤖 AI DOCUMENT ANALYZER")
    print("=" * 50)

    # Step 1: Read the document
    print("📖 Step 1: Reading document...")
    document_text = read_document(file_path)

    if document_text.startswith("ERROR"):
        return {"error": document_text}

    print(f"✅ Document read successfully ({len(document_text)} characters)")

    # Step 2: Set up AI
    print("�� Step 2: Setting up OpenAI...")
    client, model, status = setup_openai_client()

    if status != "SUCCESS":
        return {"error": status}

    print(f"✅ OpenAI ready (Model: {model})")

    # Step 3: Analyze with AI
    print("🧠 Step 3: Analyzing with AI...")
    analysis, ai_status = analyze_document_with_ai(document_text, client, model)

    print(f"✅ AI analysis complete (Status: {ai_status})")

    return analysis

def main():
    """
    Test the AI analyzer with a sample document
    """
    # Test with the referral document
    test_file = "OWL Downloads/1148_VGo_2019-11-02_2025-05-02/documents/2_ViGo_WISC.pdf"

    print("🚀 TESTING AI DOCUMENT ANALYZER")
    print("=" * 60)

    result = analyze_document(test_file)

    print("\n📊 ANALYSIS RESULTS:")
    print("=" * 50)
    print(json.dumps(result, indent=2))

    print("\n" + "=" * 60)
    print("✅ Test completed!")

if __name__ == "__main__":
    main()
