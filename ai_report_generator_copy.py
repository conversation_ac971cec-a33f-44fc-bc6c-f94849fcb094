#!/usr/bin/env python3
"""
AI Report Generator for Psychometrist Portal
Uses Groq AI to generate comprehensive psychological reports matching professional standards 
"""
import os
import json
from dotenv import load_dotenv
from openai_client import setup_openai_client
import time
from datetime import datetime
import re

# Load environment variables
load_dotenv()

def create_section_prompt(patient_analyses, section, subsections=None):
    """
    Create a prompt for an entire section, listing all its subsections.
    """
    analyses_text = ""
    for i, analysis in enumerate(patient_analyses, 1):
        analyses_text += f"\n--- DOCUMENT {i}: {analysis.get('document_type', 'Unknown')} ---"
        analyses_text += json.dumps(analysis, indent=2)
        analyses_text += "\n"

    # Style and tone instructions for all sections
    style_instructions = """
- Write in a professional, objective, formal, and clinical tone, always in the third person (e.g., 'Mr. ClientLastName reported...', 'STUDENT’s mother described...').
- Every conclusion or observation must be explicitly linked to a source (test, behavioral observation, or informant).
- Integrate test scores directly into the narrative using the format: '...his score fell in the [Descriptive Range] range (around the ##th percentile).'
- Use cautious, interpretive language (e.g., 'appears to be', 'suggests that', 'it is felt that', 'may have issues').
- Use bulleted or numbered lists where specified; otherwise, use clear, concise narrative.
- If information is missing, explicitly state so (e.g., 'No information available about family history.').
- Do not use markdown. Use valid HTML for formatting: <p> for paragraphs, <ul>/<ol> and <li> for lists, <br> for line breaks.
"""

    if section == "front_page":
        prompt = f"""
Generate the front page of the psychological report as a single HTML table. Your output MUST be ONLY a single <table>...</table> element, with no text, headings, explanations, or narrative before or after. Do NOT include any <html>, <body>, <h1>, <h2>, <p>, or any other tags. Do NOT explain, summarize, or add any content outside the table. If a value is missing, leave the cell blank. If you output anything except a single <table>...</table>, the result will be rejected.

The table should have the following rows (in this order):
- Name
- D.O.B
- Parents
- Address
- Phone
- School
- School Board
- Grade/Program
- Assessment by
- Assessment Dates
- Age at testing (age in years and months)

Use the following analyzed documents to fill in the information:
{analyses_text}
"""
        return prompt

    if subsections:
        subsections_list = "\n".join([f"- {sub[0]} (target: {sub[1]} words)" for sub in subsections])
        prompt = f"""
Generate the following subsections of the {section.replace('_', ' ').upper()} section of a psychological report. For each subsection, generate a separate HTML block, wrapped in <html>...</html>, and clearly label each subsection with its subheading as an <h2> tag at the start of the block. Do NOT repeat the main section heading. Do NOT include any text outside the HTML tags. Do NOT use markdown.

{style_instructions}

Subsections to generate:
{subsections_list}

Use the following analyzed documents to fill in the information:
{analyses_text}
"""
        return prompt
    return ""

# Helper to strip main heading from subsection output
def strip_main_heading(text, main_heading):
    # Remove lines that match the main heading (case-insensitive, with or without markdown/HTML)
    pattern = re.compile(rf"^(#*\s*)?{re.escape(main_heading)}:?\s*$", re.IGNORECASE | re.MULTILINE)
    return pattern.sub("", text).lstrip("\n")

def generate_section(client, model, section_prompt, max_retries=5):
    """
    Generate a section of the report with retry logic
    """
    retry_delay = 120  # seconds (start at 2 minutes)
    for attempt in range(max_retries):
        try:
            response = client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "user", "content": section_prompt}
                ],
                temperature=0.3,
                max_tokens=4096,  # Or your preferred value
                stop=None
            )
            return response.choices[0].message.content
        except Exception as e:
            print("OpenAI API error in generate_section:", e)
            if hasattr(e, 'response'):
                print("OpenAI response:", getattr(e.response, 'text', e.response))
            if "429" in str(e) and attempt < max_retries - 1:
                print(f"[429 ERROR] Rate limit hit, retrying in {retry_delay} seconds (attempt {attempt+1}/{max_retries})...")
                time.sleep(retry_delay)
                retry_delay *= 2  # Exponential backoff
            else:
                raise e

def save_report_preview(preview_data, output_path="report_preview.json"):
    """
    Save the current report preview to a JSON file.
    """
    with open(output_path, "w", encoding="utf-8") as f:
        json.dump(preview_data, f, ensure_ascii=False, indent=2)

def save_final_report_json(report_data, patient_name, output_dir="reports"):
    """
    Save the final report as a structured JSON file.
    """
    import os
    from datetime import datetime
    os.makedirs(output_dir, exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d")
    safe_name = "".join(c for c in patient_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
    filename = f"Psychological_Report_{safe_name}_{timestamp}.json"
    filepath = os.path.join(output_dir, filename)
    with open(filepath, 'w', encoding='utf-8') as f:
        json.dump(report_data, f, ensure_ascii=False, indent=2)
    return filepath

def extract_first_html_table(text):
    """
    Extract only the first <table>...</table> block from the text. Returns the table HTML or an empty string if not found.
    """
    import re
    match = re.search(r"<table[\s\S]*?</table>", text, re.IGNORECASE)
    return match.group(0) if match else ""

def flatten_report(report_dict):
    """
    Flatten the sectioned report dict into a single string for TXT output.
    """
    lines = []
    for section in report_dict.values():
        for sub in section.values():
            if isinstance(sub, dict) and 'content' in sub:
                lines.append(sub['content'])
            else:
                lines.append(sub)
    return '\n\n'.join(lines)

def generate_report_with_ai(patient_analyses, client, model, s3_manager=None, s3_folder='reports', patient_id=None):
    import time
    import uuid
    try:
        # Define the section/subsection structure based on your skeleton and the old JSON format
        sections_structure = [
            ("front_page", [
                ("Front Page", '''<table>
<tr><td>Name</td><td>[Client's Full Name]</td></tr>
<tr><td>D.O.B</td><td>[Client's Date of Birth]</td></tr>
<tr><td>Parents</td><td>[Parents' Names]</td></tr>
<tr><td>Address</td><td>[Client's Address]</td></tr>
<tr><td>Phone</td><td>[Phone Number(s)]</td></tr>
<tr><td>School</td><td>[School Name]</td></tr>
<tr><td>Grade/Program</td><td>[Grade and Program]</td></tr>
<tr><td>Assessment by</td><td>[Name of Psychologist(s) and Psychometrist(s)]</td></tr>
<tr><td>Assessment Dates</td><td>[List of Dates for Interviews and Testing]</td></tr>
<tr><td>Age at testing</td><td>[Client's Age in Years and Months]</td></tr>
</table>''')
            ]),
            ("reason_referral", [
                ("Reason for Referral", '''<h2>Reason for Referral</h2>\n<p>[Client's Name] was referred for a psychoeducational assessment by [Referrer's Name/Title] on [Date]. The purpose of the assessment is to address concerns about [briefly state the primary concerns, e.g., possibility of Attention Deficit Hyperactivity Disorder (ADHD), difficulties with written work, social functioning]. Should an impairment be identified, functional limitations and appropriate accommodations will be outlined</p>''')
            ]),
            ("background", [
                ("Sources of Background Information", '''<h2>Sources of Background Information</h2>\n<p>* Intake Interview with [e.g., Client and/or Parents]<br>* Clinical Interview with [e.g., Client alone]<br>* Childhood History Form, completed by [e.g., Client's mother]<br>* [Name of Questionnaire, e.g., Learning Disability Screening Questionnaire], completed by [e.g., Client and his mother]<br>* Report cards from [School Name(s) and years, e.g., Grade 1 to Grade 8]<br>* Previous assessment reports, if any<br>Note: The assessment results were interpreted in light of the background information that was provided. Results might be interpreted differently if relevant background information was omitted. For example, [mention any missing information, e.g., portions of report cards were unavailable for review]</p>'''),
                ("Note", '''<h2>Note</h2>\n<p>The assessment results were interpreted in light of the background information that was provided. Results might be interpreted differently if relevant background information was omitted. For example, [mention any missing information, e.g., portions of report cards were unavailable for review]</p>'''),
                ("Background Information", '''<h2>Background Information</h2>\n<p>[Client's Name] is a [Age]-year-old [male/female] in [Grade Level] at [School Name]. His/Her first and most dominant language is [Language]. He/She lives with [describe living situation]. His/Her favorite activities are [list activities and interests]. His/Her career aspiration is [describe future goals].</p>'''),
                ("Family Constellation", '''<h2>Family Constellation</h2>\n<p>[Client's Name] was born in [City, Province/State] and has lived there [describe residency history]. [Client's Name]'s father, [Father's Name], is a [Father's Occupation] and holds a [Father's Education Level]. [Client's Name]'s mother, [Mother's Name], is a [Mother's Occupation] and holds a [Mother's Education Level]. There is a positive family history of [list any relevant disorders, e.g., ADHD, anxiety, learning disorders].</p>'''),
                ("Developmental and Medical History", '''<h2>Developmental and Medical History</h2>\n<p>[Describe pregnancy and birth details, including any complications]. Early developmental milestones for motor skills (e.g., crawling, walking) and language skills (e.g., first words) were met in the [e.g., normal, late, early] range. Later milestones such as [e.g., riding a bicycle, tying shoelaces] were developed [e.g., late, on time]. [Client's Name] is usually in good health, with a history of [mention any medical conditions like allergies]. He/She sleeps approximately [Number] hours per night, though [mention any reported sleep issues, e.g., difficulty falling asleep, late bedtime].</p>'''),
                ("Academic History", '''<h2>Academic History</h2>\n<p>[Provide a chronological summary of the client's education, starting from Kindergarten]. In Grade [Number], teachers noted that [include specific teacher comments from report cards regarding learning skills, behavior, and academic performance]. His/Her final Learning Skills and Work Habits were rated as [e.g., "G" for good, "S" for satisfactory] in areas such as Organization and Self-Regulation. His/Her grades were in the [e.g., B to B+] range for subjects like [list subjects]. In Grade 3, [Client Name] achieved [Level #] on the EQAO standardized assessment for [list subjects]. [Continue this chronological summary for subsequent grades, highlighting patterns in performance, teacher feedback, and any changes in school programs].</p>'''),
                ("Presenting Concerns", '''<h2>Current Concerns</h2>\n<p>The client's current teacher, [Teacher's Name], reports that [summarize teacher's main concerns regarding academics, attention, and social functioning]. The client's parents report concerns with [summarize parent's concerns, e.g., attention, organization, social skills, emotional regulation]. [Client's Name] reports that he/she finds [e.g., reading, writing, math] to be [e.g., difficult, manageable] and struggles with [summarize client's self-reported challenges].</p>''')
            ]),
            ("assessment_method", [
                ("Tests Administered", '''<h2>Tests Administered</h2>\n<p>* Wechsler Individual Achievement Test (WIAT)<br>* Wechsler Intelligence Scale for Children/Adults (WISC/WAIS)<br>* Conners Continuous Performance/Auditory Test of Attention (CPT/CATA)<br>* Test of Word Reading Efficiency (TOWRE)<br>* Behavior Assessment System for Children (BASC-3)<br>* [List any other specific tests administered, e.g., NEPSY-II, Beery VMI, CVLT-II]</p>''')
            ]),
            ("Behavioral Observations", [
                ("Behavioral Observations", '''<h2>Behavioral Observations</h2>\n<p>[Client's Name] was assessed over the course of [Number] sessions. He/She presented as a [describe appearance and initial demeanor, e.g., quiet and reserved, polite and friendly] young [man/woman]. Rapport was established [e.g., easily, with some coaxing]. His/Her approach to tasks was [describe work style, e.g., confident, hesitant, focused]. He/She persevered when tasks became challenging and attempted all items presented. Overall, [Client's Name] was [describe overall impression, e.g., polite and a pleasure to assess]. The results of the assessment are considered to be reliable and valid estimates of his/her current levels of functioning.</p>''')
            ]),
            ("assessment_results", [
                ("Academic Skills", '''<h2>Academic Skills</h2>\n<p>* Oral Language: On a task measuring expressive vocabulary, his/her score fell within the [Descriptive Range] range (around the ##th percentile). His/her listening comprehension performance fell in the [Descriptive Range] range (around the ##th percentile).<br>* Reading: On an untimed task of decoding real words, performance fell in the [Descriptive Range] range (around the ##th percentile). His/Her score on reading comprehension fell within the [Descriptive Range] range (around the ##th percentile).<br>* Writing: His/Her sentence composition score fell within the [Descriptive Range] range (around the ##th percentile). The overall essay composition score was in the [Descriptive Range] range (around the ##th percentile).<br>* Mathematics: On a measure of math problem-solving skills, the score was within the [Descriptive Range] range (around the ##th percentile). Performance on math fluency fell in the [Descriptive Range] range (around the ##th percentile).</p>'''),
                ("Executive Functioning Skills", '''<h2>Executive Functioning Skills</h2>\n<p>On tasks measuring inhibitory control, performance fell in the [Descriptive Range] range. On a test of visual organization and planning, the score fell in the [Descriptive Range] range (around the ##th percentile).</p>'''),
                ("Cognitive Abilities", '''<h2>Cognitive Abilities</h2>\n<p>[Client Name]'s overall reasoning skills as measured by the Full-Scale IQ (FSIQ) fell within the [Descriptive Range] range (around the ##th percentile). The Verbal Comprehension Index score was in the [Descriptive Range] range (around the ##th percentile). The Fluid Reasoning Index score fell within the [Descriptive Range] range (around the ##th percentile)</p>'''),
                ("Attention, Concentration and Working Memory", '''<h2>Attention, Concentration and Working Memory</h2>\n<p>On the Working Memory Index, the score fell within the [Descriptive Range] range (around the ##th percentile) On the CPT-3, a computerized test of visual sustained attention, his/her profile of scores indicated [describe findings, e.g., a high likelihood of having a disorder characterized by attention deficits].</p>'''),
                ("Memory", '''<h2>Memory</h2>\n<p>His/her overall memory score on tasks of verbal and visual memory fell in the [Descriptive Range] range (around the ##th percentile). On tasks of verbal memory, the overall score was in the [Descriptive Range] range. On tasks of visual memory, the overall score fell in the [Descriptive Range] range</p>'''),
                ("Processing Speed", '''<h2>Processing Speed</h2>\n<p>On the Processing Speed Index, the overall score fell within the [Descriptive Range] range (around the ##th percentile)</p>'''),
                ("Behavioral and Psychosocial Functioning", '''<h2>Behavioral and Psychosocial Functioning</h2>\n<p>* Parent Report: On questionnaires like the BASC-3 and Conners 3, parents reported [clinically significant/at-risk/average] levels of [e.g., attention problems, hyperactivity, anxiety]<br>* Teacher Report: The teacher's ratings on the BASC-3 and Conners 3 indicated [clinically significant/at-risk/average] levels of [e.g., attention problems, study skills, social skills].<br>* Self-Report: [Client Name]'s self-report responses on questionnaires revealed [e.g., at-risk scores on attitude to school, very elevated scores on learning problems].</p>''')
            ]),
            ("clinical_impressions", [
                ("Summary of Key Findings", '''<h2>Summary of Key Findings</h2>\n<p>[Provide a narrative summary integrating all the assessment findings. Start with a general statement about the client's presentation and key struggles. Discuss the cognitive and academic profile, highlighting the pattern of strengths and weaknesses.]</p>'''),
                ("Integration of Assessment Data", '''<h2>Integration of Assessment Data</h2>\n<p>A summary of [Client's Name]’s learning profile is found below:<br>* Normative Strengths (Above Average range):<br>    * [List skills, e.g., Reading comprehension of an essay]<br>* Well-Developed Abilities (Average range):<br>    * [List skills, e.g., Verbal reasoning, Vocabulary skills]<br>* Personal, Relative Weaknesses (Low Average range):<br>    * [List skills, e.g., Listening comprehension, Math fluency]<br>* Normative Weaknesses and Impairment (Below Average to Extremely Low ranges):<br>    * [List skills, e.g., Working memory, Sustained attention]</p>'''),
                ("Diagnostic Conclusions", '''<h2>Diagnostic Conclusions</h2>\n<p>Taken together, the client's cognitive profile, developmental history, and reports from parents, teachers, and self are consistent with a DSM-V diagnosis of [State the Diagnosis, e.g., Attention-Deficit/Hyperactivity Disorder, Combined Presentation (ADHD:C) or Level 1 Autism Spectrum Disorder (ASD)]. [Provide justification for the diagnosis, referencing specific evidence from the report, such as the presence of symptoms since childhood, impairment in multiple settings, and consistency of test results with the diagnostic criteria. Rule out other diagnoses if applicable]. The client's symptoms cannot be better explained by any other intellectual, situational, or medical factors</p>'''),
                ("Impact on Functioning", '''<h2>Impact on Functioning</h2>\n<p>[Describe the impact of the diagnosis and findings on the client's daily functioning, academic performance, and social/emotional well-being.]</p>''')
            ]),
            ("recommendations", [
                ("Medical/Clinical Recommendations", '''<h2>Medical/Clinical Recommendations</h2>\n<p>Based on the assessment findings, the following recommendations are made to support [Client Name]:<br>1. Educational: It is recommended that [Client Name] receive an Individual Education Plan (IEP). Accommodations should include [e.g., extra time (approximately 25-30%) on tests and exams, access to a quiet room for assessments, and note-taking assistance].<br>2. Instructional: Direct teaching is recommended for [e.g., organization and time management skills]. This can include [e.g., using visual schedules, breaking down large tasks into smaller parts, and using techniques like SQ5R for reading].<br>3. Clinical/Therapeutic: [Client Name] and his/her parents should consult their family doctor about [e.g., the possibility of a trial of medication to manage ADHD symptoms]. It is also recommended that [Client Name] seek [e.g., individual psychotherapy, family therapy] with a mental health professional specializing in [e.g., ADHD, ASD, Gaming Disorder]<br>4. Home/Family: It is recommended that parents [e.g., consult relevant resources for parenting strategies, seek family therapy, monitor and limit screen time].<br>5. Community Resources and Reading: Parents and [Client Name] may wish to consult the following resources:<br>    * Organizations: [e.g., CADDRA (www.caddra.ca), Autism Ontario (www.autismontario.com)]<br>    * Books: [e.g., Smart, But Scattered For Teens, A Parent’s Guide to High-Functioning Autism Spectrum Disorder]<br>The above clinical impressions and recommendations were communicated to [e.g., Client's Name and his/her mother], via [e.g., video conference], on [Date].</p>''')
            ])
        ]

        # Define word limits for each subheading (user can adjust these as needed)
        section_word_limits = {
            "Reason for Referral": 150,
            "Note": 60,
            "Background Information": 150,
            "Family Constellation": 120,
            "Developmental and Medical History": 550,
            "Academic History": 250,
            "Presenting Concerns": 350,
            "Behavioral Observations": 250,
            "Academic Skills": 400,
            "Executive Functioning Skills": 550,
            "Cognitive Abilities": 310,
            "Attention, Concentration and Working Memory": 420,
            "Memory": 200,
            "Processing Speed": 220,
            "Behavioral and Psychosocial Functioning": 1350,
            "Summary of Key Findings": 300,
            "Integration of Assessment Data": 400,
            "Diagnostic Conclusions": 400,
            "Impact on Functioning": 300,
            "Medical/Clinical Recommendations": 2000,
        }
        # Compose analyses text
        analyses_text = ""
        for i, analysis in enumerate(patient_analyses, 1):
            analyses_text += f"\n--- DOCUMENT {i}: {analysis.get('document_type', 'Unknown')} ---"
            analyses_text += json.dumps(analysis, indent=2)
            analyses_text += "\n"

        # Prepare the report preview dict
        report_preview = {}
        for section_key, subsections in sections_structure:
            report_preview[section_key] = {}
            for subheading, template in subsections:
                word_limit = section_word_limits.get(subheading, 500)
                prompt = f'''
You are an expert psychological report writer. Using only the information in the analyzed documents below, generate the following section of a psychological report. Fill in all placeholders with the most relevant information from the documents. If information is missing, leave the placeholder in brackets and state "No information available". Do not add, remove, or reorder sections. Do not use markdown. Use only valid HTML as in the template. This section should not exceed {word_limit} words. Be concise but thorough. Use a professional, objective, and clinical tone. Integrate test scores and behavioral observations as appropriate. If a section is not applicable, state so explicitly.

TEMPLATE:
{template}

ANALYZED DOCUMENTS:
{analyses_text}
'''
        section_content = generate_section(client, model, prompt, max_retries=5)
        report_preview[section_key][subheading] = section_content
        save_report_preview(report_preview)  # Save after each section for progress
        time.sleep(1)  # Small delay to avoid rate limits
        # Try to get patient name for filename
        patient_name = "Unknown_Patient"
        if patient_analyses and isinstance(patient_analyses, list):
            for analysis in patient_analyses:
                if isinstance(analysis, dict) and "patient_info" in analysis and "name" in analysis["patient_info"]:
                    patient_name = analysis["patient_info"]["name"]
                    break
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        safe_name = "".join(c for c in patient_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
        json_filename = f"{safe_name}_{timestamp}_psychological_report.json"
        if s3_manager:
            s3_upload_result = s3_manager.upload_json_data(
                data=report_preview,
                filename=json_filename,
                folder=s3_folder.rstrip('/'),
                metadata={
                    'patient-id': patient_id or '',
                    'patient-name': patient_name,
                    'content-type': 'application/json'
                }
            )
            json_s3_key = s3_upload_result["s3_key"]
        else:
            json_s3_key = None
        return report_preview, "SUCCESS", json_s3_key
    except Exception as e:
        return f"ERROR generating report: {str(e)}", "ERROR", None

def generate_psychological_report(patient_analyses, s3_manager=None, s3_folder='reports', patient_id=None):
    """
    Main function to generate a psychological report from multiple analyses
    """
    print(f"ð  AI PSYCHOLOGICAL REPORT GENERATOR")
    print("=" * 60)
    # Step 1: Validate input
    if not patient_analyses or len(patient_analyses) == 0:
        return {"error": "No patient analyses provided"}
    print(f"ð  Processing {len(patient_analyses)} document analyses...")
    # Step 2: Set up AI
    print("ð § Setting up OpenAI...")
    client, model, status = setup_openai_client()
    if status != "SUCCESS":
        return {"error": status}
    print(f"â  OpenAI ready (Model: {model})")
    # Step 3: Generate report with AI
    print("ð §  Generating comprehensive psychological report...")
    report_dict, ai_status, final_json_path = generate_report_with_ai(
        patient_analyses, client, model, s3_manager=s3_manager, s3_folder=s3_folder, patient_id=patient_id
    )
    print(f"â  Report generation complete (Status: {ai_status})")
    if ai_status == "SUCCESS":
        txt_report = flatten_report(report_dict)
        return {"report": report_dict, "report_txt": txt_report, "status": "SUCCESS", "json_s3_key": final_json_path}
    else:
        return {"error": report_dict, "status": ai_status, "json_s3_key": final_json_path}

def save_report_to_file(report_content, patient_name, output_dir="reports"):
    """
    Save the generated report to a file with professional naming
    """
    import os
    from datetime import datetime
    # Create reports directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    # Create filename with patient name and timestamp
    timestamp = datetime.now().strftime("%Y%m%d")
    safe_name = "".join(c for c in patient_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
    filename = f"Psychological_Report_{safe_name}_{timestamp}.docx"
    filepath = os.path.join(output_dir, filename)
    # Save report
    with open(filepath, 'w', encoding='utf-8') as f:
        f.write(report_content)
    return filepath

def main():
    """
    Test the report generator with sample analyses
    """
    # Sample analysis data matching real psychological assessments
    sample_analyses = [
        {
            "document_type": "WISC-V Canadian Score Report",
            "patient_info": {
                "name": "Ken Wilson",
                "age": "8 years 5 months",
                "date_of_birth": "2014/03/15",
                "gender": "Male",
                "address": "123 Main Avenue, Ottawa, ON K1A 0B2",
                "phone": "************ (mom), ************ (dad)",
                "school": "Elmdale Public School",
                "school_board": "Ottawa Carleton District School Board",
                "grade": "Grade 3, Early French Immersion"
            },
            "assessment_details": {
                "psychologist": "Dr. Deanna Drahovzal",
                "psychometrist": "Ms. Smith",
                "assessment_dates": [
                    "July 21, 2022 (Intake Interview)",
                    "August 18 & 25, 2022 (Testing)",
                    "September 6, 2022 (Clinical Interview)"
                ]
            },
            "referral_info": {
                "referrer": "pediatrician",
                "referral_date": "May 2, 2022",
                "reason": "recently diagnosed with ADHD by pediatrician, purpose is to rule out other possible diagnoses and address struggles with impulsivity, self-regulation, and anxiety"
            },
            "test_results": {
                "WISC-V": {
                    "Full_Scale_IQ": "High Average range (84th percentile)",
                    "GAI": "Above Average range (95th percentile)",
                    "VCI": "Above Average range (95th percentile)",
                    "VSI": "Exceptionally High range (99th percentile)",
                    "FRI": "High Average range (79th percentile)",
                    "WMI": "Average range (50th percentile)",
                    "PSI": "Low Average range (23rd percentile)"
                },
                "WIAT-III": {
                    "Oral_Language": {
                        "Expressive_Vocabulary": "Exceptionally High range (99th percentile)",
                        "Listening_Comprehension": "Exceptionally High range (98th percentile)"
                    },
                    "Reading": {
                        "Decoding": "Average range (66th percentile)",
                        "Comprehension": "Above Average range (92nd percentile)"
                    },
                    "Writing": {
                        "Spelling": "Average range (58th percentile)",
                        "Essay_Composition": "Low Average range (10th percentile)"
                    },
                    "Math": {
                        "Reasoning": "Above Average range (92nd percentile)",
                        "Calculation": "Average range (55th percentile)"
                    }
                }
            },
            "behavioral_observations": {
                "appearance": "reserved initially but established rapport",
                "behavior": "often appeared annoyed, distracted, and disinterested",
                "attention": "low tolerance for frustrating questions, did not persevere",
                "social": "struggled with reciprocal interactions, gave short answers"
            },
            "diagnostic_impressions": [
                "Attention Deficit Hyperactivity Disorder - Combined Type (ADHD:C)",
                "High Ability Learner"
            ],
            "confidence_level": "High"
        }
    ]
    print("ð � � �  TESTING PSYCHOLOGICAL REPORT GENERATOR")
    print("=" * 60)
    result = generate_psychological_report(sample_analyses)
    if result.get("status") == "SUCCESS":
        print("ð � � �  GENERATED REPORT:")
        print("=" * 60)
        print(result["report"][:1000] + "...")  # Print first 1000 chars to avoid overwhelming output
        # Save report to file
        patient_name = sample_analyses[0]["patient_info"]["name"]
        filepath = save_report_to_file(result["report"], patient_name)
        print(f"ð � � ¾ Professional report saved to: {filepath}")
    else:
        print(f"â � �  ERROR: {result.get('error')}")
    print("\n" + "=" * 60)
    print("â � �  Test completed!")

if __name__ == "__main__":
    main()
